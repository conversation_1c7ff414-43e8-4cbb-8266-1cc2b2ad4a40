'use client';
import { ECreateAirdropEventStep, GasFeePaymentMethod } from '@/components/airdrop/event/common';
import AirdropDetailStep from '@/components/airdrop/event/create/step/airdropDetailStep';
import StepIndicator from '@/components/meme-lab/StepIndicator';
import HeaderTitle from '@/features/layout/HeaderTitle';
import { FormProvider, useForm } from 'react-hook-form';
import {
  eventAirdropSchema,
  EventAirdropSchemaFormType,
} from '@/components/airdrop/event/create/schema';
import { zodResolver } from '@hookform/resolvers/zod';
import EventDetailStep from '@/components/airdrop/event/create/step/EventDetailStep';

const defaultValues = {
  step: ECreateAirdropEventStep.EVENT_DETAIL,
  airdropTokenAddress: '',
  airdropQuantity: '',
  airdropAddress: [],
  description: '',
  descriptionImages: [],
  socials: [{ name: 'twitterLink', url: '' }],
  eventName: '',
  eventStartedAt: undefined,
  gasFeePayment: GasFeePaymentMethod.IN_APP_CLAIM,
};

export default function CreateAirdropEventPage() {
  const methods = useForm<EventAirdropSchemaFormType>({
    resolver: zodResolver(eventAirdropSchema),
    defaultValues,
  });

  const { handleSubmit, watch } = methods;

  const onSubmit = (data: EventAirdropSchemaFormType) => {
    console.log('Submit data', data);
  };

  const currentStep = watch('step');

  return (
    <div className="flex flex-col items-center justify-center pb-8">
      <HeaderTitle title="Create AirDrop Event" />
      <StepIndicator
        currentStep={currentStep}
        totalSteps={3}
        className="sticky top-0 left-0 z-10 h-[7px] w-full"
      />
      <FormProvider {...methods}>
        <form
          id="create-airdrop-event"
          onSubmit={handleSubmit(onSubmit)}
          className="w-full max-w-[764px] relative overflow-hidden rounded-std"
        >
          {currentStep === ECreateAirdropEventStep.AIRDROP_DETAIL && <AirdropDetailStep />}
          {currentStep === ECreateAirdropEventStep.EVENT_DETAIL && <EventDetailStep />}
        </form>
      </FormProvider>
    </div>
  );
}
