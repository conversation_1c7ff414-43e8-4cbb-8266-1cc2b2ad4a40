import DatabaseIcon from '@/public/icons/database.svg';
import { Controller, useFormContext } from 'react-hook-form';
import { EventAirdropSchemaFormType } from '../schema';
import clsx from 'clsx';
import FormField from '@/components/shared/FormField';
import DatePicker from 'react-datepicker';

export default function EventDetailStep() {
  return (
    <div className="w-full rounded-std bg-white py-6 px-5 flex flex-col gap-6">
      <div className="w-full flex items-center gap-[18px]">
        <div className="rounded-full bg-primary-500/10 flex items-center justify-center size-12">
          <DatabaseIcon />
        </div>
        <span className="text-gray-800 font-medium text-xl">Event Details</span>
      </div>
      <FormField name="eventName" label="Event Name" placeholder="Name" limit={13}>
        <div
          className={clsx(
            'w-full border flex justify-between rounded-xl p-4 bg-gray-300 text-gray-600 placeholder:text-gray-600 focus:outline-none',
            className,
            error ? 'border-red-500' : 'border-gray-300'
          )}
        >
          <input
            {...field}
            {...props}
            onChange={handleChange}
            className="w-full bg-transparent focus:outline-none placeholder:text-gray-600"
          />
        </div>
        {error && <p className="text-red-500 text-xs mt-1">{fieldState.error?.message}</p>}
      </FormField>
      <DatePicker selected={startDate} onChange={(date) => setStartDate(date)} />
    </div>
  );
}
