import Image from 'next/image';
import { IAirdropSpInfo } from '@/services/airdropSp/airdrop.types';
import React, { useMemo, useState } from 'react';
import Smapocke from '@/public/icons/smapocke_v2.png';
import { useGetUserRank } from '@/services/auth';
import { routePaths, SOCIAL_LINKS } from '@/constants/common';
import { useAuth } from '@/hooks/use-auth';
import useTokenAccountBuyOwnerBalance from '@/hooks/use-token-account-by-owner-balance';
import { useGetSummaryPoint } from '@/services/airdrop';
import {
  formatNumberWithCommas,
  formatNumber,
  formatNumberWithSubscriptZeros,
} from '@/utils/common';
import { PublicKey } from '@solana/web3.js';
import clsx from 'clsx';
import { range, round } from 'lodash';
import Link from 'next/link';
import { IoIosArrowForward } from 'react-icons/io';
import ImageModal from '@/components/shared/ImageModal';

enum AIRDROP_ADDRESS_TYPE {
  TOKEN_HOLDER = 'TOKEN_HOLDER',
  NFT_HOLDER = 'NFT_HOLDER',
  IMPORT_FILE = 'IMPORT_FILE',
  X_REPLY_IMPORT = 'X_REPLY_IMPORT',
}

interface Props {
  airdropDetail: any;
}
export default function UpComingAirdropEventSection({ airdropDetail }: Props) {
  const { user } = useAuth();
  const [imageModalSrc, setImageModalSrc] = useState<string>('');
  const { data: tokenBalances, isLoading: isLoadingBalance } = useTokenAccountBuyOwnerBalance(
    user && user.solana_address ? new PublicKey(user?.solana_address) : undefined,
    'mainnet'
  );
  const currentTokenBalance = tokenBalances?.find(
    (token) => token.mint === airdropDetail.tokenAddress
  );
  const balance = currentTokenBalance
    ? formatNumber(currentTokenBalance?.tokenAmount.uiAmountString || 0, 2)
    : 0;
  const getSocialLinks = () => {
    return SOCIAL_LINKS.map((social) => {
      return {
        ...social,
        url: '#',
      };
    }).filter(Boolean);
  };

  const socialItems = useMemo(() => {
    return getSocialLinks();
  }, []);

  return (
    <div className="w-full flex flex-col gap-5">
      <div className="w-full flex flex-col gap-2">
        <div className="flex justify-between items-center text-primary-500 bg-primary-500/10 rounded-2xl px-5 py-2">
          <span className="text-sm font-medium">Your Airdrop Allocation</span>
          <div className="text-xl font-medium">
            <span className="text-sm">
              {formatNumberWithSubscriptZeros(airdropDetail.airdropPercent.toString(), 2)}%
            </span>
            <span className="text-xs"> / 100%</span>
          </div>
        </div>
        <Link className="flex gap-[18px] py-3.5" href="#">
          <Image
            src={airdropDetail.icon || '/images/smart-pocket-default-image.png'}
            alt={'Airdrop Logo'}
            width={48}
            height={48}
            className="rounded-full size-12 object-cover"
          />
          <div className="w-full flex items-center gap-[18px]">
            <div className="flex flex-col gap-2">
              <div className="font-medium text-gray-900 leading-none">
                {airdropDetail.type === AIRDROP_ADDRESS_TYPE.X_REPLY_IMPORT
                  ? 'X Post Reply'
                  : airdropDetail.tokenSymbol}
              </div>
              <span className="text-sm text-gray-600 leading-none">{airdropDetail.tokenName}</span>
            </div>
            <div className="grow flex gap-2.5 items-center justify-end">
              <span
                className={clsx('font-medium text-gray-900 leading-none', {
                  'animate-pulse': isLoadingBalance,
                })}
              >
                {balance ? balance : '0'} {airdropDetail.tokenSymbol}
              </span>
              {true ? (
                <IoIosArrowForward className="text-gray-600 size-4" />
              ) : (
                <span className="size-4 invisible"></span>
              )}
            </div>
          </div>
        </Link>
      </div>
      <div className="grid grid-cols-4 gap-3">
        {range(4).map((imageUrl, index) => (
          <div
            key={index}
            className="relative group aspect-square w-full cursor-pointer"
            onClick={() => setImageModalSrc('/images/smart-pocket-default-image.png')}
          >
            <Image
              src={'/images/smart-pocket-default-image.png'}
              alt={`Description image ${index + 1}`}
              width={100}
              height={100}
              className="rounded-[12px] w-full aspect-square object-cover"
            />
          </div>
        ))}
        <ImageModal
          isOpen={!!imageModalSrc}
          onClose={() => setImageModalSrc('')}
          src={imageModalSrc}
        />
      </div>
      <p className="text-sm input-text-gray-600-black whitespace-pre-line">{`Pockemy, the official character of Smart Pocket.\nLet’s blast off into space together!`}</p>
      <div className="flex flex-wrap gap-2">
        {socialItems.map((socialItem) => {
          return (
            <span
              key={socialItem?.key}
              className="flex items-center justify-center gap-[10px] bg-[#F0EEEE] rounded-full py-[8px] px-[14px] text-sm font-medium text-[#666666] cursor-pointer"
            >
              {React.createElement(socialItem?.icon)}
            </span>
          );
        })}
      </div>
    </div>
  );
}
