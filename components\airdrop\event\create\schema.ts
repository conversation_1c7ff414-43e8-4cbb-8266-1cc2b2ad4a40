import { z } from 'zod';
import { ECreateAirdropEventStep, GasFeePaymentMethod } from '../common';
import { getFlexibleSocialUrlErrorMessage, isValidSocialUrl } from '@/utils/common';

export const eventAirdropSchema = z
  .object({
    step: z.enum(ECreateAirdropEventStep),
    airdropTokenAddress: z.string().min(1, 'Token address is required'),
    airdropQuantity: z.string().min(1, 'Airdrop Quantity is required'),
    airdropAddress: z.array(z.string()).optional(),
    description: z.string(),
    descriptionImages: z.array(z.string()).optional(),
    socials: z
      .array(
        z.object({
          name: z.string().optional(),
          url: z.string().optional(),
        })
      )
      .optional(),
    eventName: z
      .string()
      .min(1, 'Event name is required')
      .max(13, 'Event name must be less than 13 characters')
      .default(''),
    eventStartedAt: z.date().optional(),
    gasFeePayment: z.enum(GasFeePaymentMethod),
  })
  .superRefine((data, ctx) => {
    if (data.step === ECreateAirdropEventStep.EVENT_DETAIL) {
      data.socials?.forEach((social, index) => {
        const { name, url } = social;
        if (url && typeof url !== 'string') {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['socials', index, 'url'],
            message: 'Invalid URL.',
          });
        }
        if (url && url.trim().length === 0) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['socials', index, 'url'],
            message: 'Field cannot be only whitespace',
          });
        }

        if (
          url &&
          (!isValidSocialUrl(url.trim(), name) ||
            !getFlexibleSocialUrlErrorMessage(url.trim(), name!))
        ) {
          ctx.addIssue({
            code: z.ZodIssueCode.custom,
            path: ['socials', index, 'url'],
            message: 'Invalid Social Url',
          });
        }
      });
    }
  });

export type EventAirdropSchemaFormType = z.infer<typeof eventAirdropSchema>;
