import clsx from 'clsx';
import { ChangeEvent, useCallback } from 'react';
import { Controller, useController, useFormContext } from 'react-hook-form';

interface FormFieldProps {
  label: string;
  limit?: number;
  optional?: boolean;
  render?: React.ReactNode;
}
const FormField = ({ label, limit, optional, render }: FormFieldProps) => {
  const error = !!fieldState.error?.message;
  return (
    <div className="flex flex-col gap-[10px]">
      <div className="flex justify-between items-center">
        <div className="flex items-center">
          <label className="text-[#666666] text-base font-normal ml-2">{label}</label>
        </div>
        <span className="text-[#666666] text-xs font-normal">
          {limit ? limit : optional ? 'Optional' : ''}
        </span>
      </div>
      <div className="flex flex-col gap-1">{render}</div>
    </div>
  );
};

export default FormField;
