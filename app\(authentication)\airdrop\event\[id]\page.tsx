'use client';
import InProgressAirdropEventSection from '@/components/airdrop/event/detail/InProgressAirdropEventSection';
import UpComingAirdropEventSection from '@/components/airdrop/event/detail/UpComingAirdropEventSection';
import AirdropBg from '@/public/images/airdrop-bg.png';
import { formatNumberWithCommas } from '@/utils/common';
import clsx from 'clsx';
import { format } from 'date-fns';
import Image from 'next/image';

const dummyDetail = {
  expectedAmount: 10000,
  airdropDate: '2025-9-1',
  tokenSymbol: 'PKM',
  tokenName: 'Pokemy',
  airdropPercent: 10.12345,
  tokenAddress: '2iPczEEZYRLxX5uJTyaUxAnRRHZBvLtivMopbU6SPHSp',
  type: 'X_REPLY_IMPORT',
};

export default function AirdropEventPage() {
  const isInProgress = new Date().getTime() >= new Date(dummyDetail.airdropDate).getTime();
  return (
    <div className="flex flex-col relative no-layout grow">
      <div
        className="w-full h-60 flex flex-col-reverse absolute top-0 sm:rounded-std overflow-hidden"
        style={{
          backgroundImage: `url(${AirdropBg.src})`,
          backgroundRepeat: 'no-repeat',
          backgroundSize: 'cover',
          zIndex: '1',
        }}
      >
        <div
          className="h-36"
          style={{
            background:
              'linear-gradient(180deg, rgba(245, 245, 245, 0) 0%, rgba(245, 245, 245, 0.6) 48.76%, #F5F5F5 96.56%)',
          }}
        ></div>
      </div>
      <div className="flex flex-col relative z-10 pt-7 px-[18px] grow">
        <div className="flex flex-col gap-5 items-center grow">
          <div className="size-40 bg-white rounded-full flex items-center justify-center drop-shadow-[0_8px_0_#E3DEDE]">
            <Image
              src={'/images/smart-pocket-default-image.png'}
              alt="Airdrop Thumbnail"
              width={145}
              height={145}
              layout="intrinsic"
              className="size-[145px] rounded-full object-cover"
            />
          </div>
          <div className="flex flex-col text-center">
            <span className="text-gray-800 text-xl font-medium">
              {!isInProgress && 'Be ready for'} ${dummyDetail.tokenSymbol} AirDrop!!
            </span>
            <span className="text-gray-600 text-base ">
              {isInProgress ? 'Now Live' : format(dummyDetail.airdropDate, 'dd MMM yyyy')}
            </span>
          </div>
          <div className="w-full flex flex-col gap-2.5 grow">
            <div className="flex flex-col bg-white rounded-std pt-5 pb-2.5 px-[18px] gap-2.5 grow">
              <div className="flex flex-col">
                <div className="w-full flex flex-col gap-1 bg-gray-300 py-4 px-5 rounded-2xl">
                  <span className="text-gray-600 text-sm font-medium">
                    Total ${dummyDetail.tokenSymbol} Airdrop
                  </span>
                  <div className="w-full flex justify-between">
                    {/* TODO: add loading state */}
                    <span
                      className={clsx(
                        'text-gray-800 text-xl font-medium',
                        false && 'animate-pulse'
                      )}
                    >
                      {formatNumberWithCommas(dummyDetail?.expectedAmount, 2)}
                      <span className="text-sm"> {dummyDetail.tokenSymbol}</span>
                    </span>
                    <span className="rounded-full py-1.5 px-4 bg-white text-gray-800 text-xs font-medium">
                      Today :&nbsp;
                      {/* // TODO: add loading state */}
                      <span className={clsx('text-green-600', false && 'animate-pulse')}>
                        {/* TODO: fix data display */}
                        +0SP
                        {/* <NumberDisplay number={'1234'} /> */}
                      </span>
                    </span>
                  </div>
                </div>
                {isInProgress ? (
                  <div className="mt-2.5">
                    <InProgressAirdropEventSection airdropDetail={dummyDetail} />
                  </div>
                ) : (
                  <div className="mt-4 mb-2.5">
                    <UpComingAirdropEventSection airdropDetail={dummyDetail} />
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
